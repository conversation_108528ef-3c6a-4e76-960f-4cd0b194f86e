[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vups"
version = "0.1.0"
description = "Tool Chain for vup infromation"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiohttp>=3.12.15",
    "aiolimiter>=1.2.1",
    "aiotieba>=4.6.0",
    "asyncpg>=0.30.0",
    "bilibili-api-python>=17.3.0",
    "dashscope>=1.24.2",
    "dateparser>=1.2.2",
    "fastapi[all]>=0.116.1",
    "jieba>=0.42.1",
    "langchain-community>=0.3.27",
    "langchain-mcp-adapters>=0.1.9",
    "langchain-openai>=0.3.31",
    "langgraph>=0.6.6",
    "loguru>=0.7.3",
    "matplotlib>=3.10.5",
    "mcp[cli]>=1.13.1",
    "numpy>=2.2.6",
    "pathlib>=1.0.1",
    "pycryptodome>=3.23.0",
    "python-dotenv>=1.1.1",
    "requests>=2.32.5",
    "tabulate>=0.9.0",
    "wordcloud>=1.9.4",
]

[tool.setuptools.packages.find]
include = ["vups*", "vups_server*"]

[dependency-groups]
dev = [
    "ruff>=0.12.10",
]
