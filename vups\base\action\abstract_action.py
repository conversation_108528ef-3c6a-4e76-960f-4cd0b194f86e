from abc import ABC

from vups.base.llm import LLM_DICT
from vups.base.llm.mcp_client import MCPClient


class Action(ABC):
    def __init__(
        self,
        llm_name="hunyuan-standard-256K",
        name: str = "",
        context=None,
        **kwargs,
    ):
        self.name: str = name
        self.context = context
        self.prefix = ""
        self.profile = ""
        self.desc = ""
        self.content = ""
        self.instruct_content = None

        self.llm = LLM_DICT[llm_name]
        self.mcp_client = MCPClient(self.llm)

    def set_prefix(self, prefix, profile):
        """Set prefix for later usage"""
        self.prefix = prefix
        self.profile = profile

    def __str__(self):
        return self.__class__.__name__

    def __repr__(self):
        return self.__str__()

    # async def _aask(self, prompt: str, system_msgs: Optional[list[str]] = None) -> str:
    #     """Append default prefix"""
    #     if not system_msgs:
    #         system_msgs = []
    #     system_msgs.append(self.prefix)
    #     return await self.llm.aask(prompt, system_msgs)

    async def run(self, *args, **kwargs):
        """Run action"""
        raise NotImplementedError("The run method should be implemented in a subclass.")
