# Alibaba Paraformer ASR - Different modes
import asyncio
import os
from vups.algos.tools.asr import AliParaformerASR, BCutASR
from vups.algos.tools.video_dump import VideoSourceDownloader
from vups.logger import logger

test_file = "tests/vups/data/tafei.mp3"

# ali_asr = AliParaformerASR(
#     api_key=os.getenv("DASHSCOPE_API_KEY"),
#     streaming_mode=True,
# )
# result = asyncio.run(ali_asr.transcribe(test_file, ["zh", "en"], file_format="mp3", sample_rate=32000))
# logger.info(f"AliParaformerASR result: {result}")
# BCut ASR - For Bilibili processing
# bcut_asr = BCutASR()
# result = asyncio.run(bcut_asr.transcribe(test_file))
# logger.info(f"BCutASR result: {result}")


ali_asr = AliParaformerASR(
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    streaming_mode=False,
)

vad = VideoSourceDownloader()

async def main():
    # await vad.download_audio("BV1hs4y1c7V3", output_filename="tests/vups/data/test_audio.mp3")
    result = await ali_asr.transcribe("tests/vups/data/test_audio.mp3", ["zh", "en"], file_format="mp3", sample_rate=16000)
    # os.remove("tests/vups/data/test_audio.mp3")
    logger.info(f"AliParaformerASR result: {result}")

asyncio.run(main())
